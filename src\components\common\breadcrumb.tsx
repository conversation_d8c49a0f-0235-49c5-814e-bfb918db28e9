'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { BreadcrumbItem } from '@/lib/types/types';

interface BreadcrumbProps {
  data?: BreadcrumbItem[];
}

const pathToBreadcrumb: Record<string, BreadcrumbItem[]> = {
  '/dashboard': [{ label: 'Dashboard' }],
  '/packages': [{ label: 'Packages' }],
  '/packages/manage-package': [
    { label: 'Packages', href: '/packages' },
    { label: 'Manage Packages' },
  ],
  '/packages/new-package': [
    { label: 'Packages', href: '/packages' },
    { label: 'New Package' },
  ],
  '/packages/category': [
    { label: 'Packages', href: '/packages' },
    { label: 'Categories' },
  ],
  '/packages/manage-category': [
    { label: 'Packages', href: '/packages' },
    { label: 'Manage Categories' },
  ],
  '/packages/investigation': [
    { label: 'Packages', href: '/packages' },
    { label: 'Investigations' },
  ],
  '/feedbacks': [{ label: 'Feedbacks' }],
  '/cafeteria': [{ label: 'Cafeteria' }],
  '/cafeteria/menu': [
    { label: 'Cafeteria', href: '/cafeteria' },
    { label: 'Menu Management' },
  ],
  '/cafeteria/orders': [
    { label: 'Cafeteria', href: '/cafeteria' },
    { label: 'Orders' },
  ],
  '/referrals': [{ label: 'Referrals' }],
  '/referrals/create': [
    { label: 'Referrals', href: '/referrals' },
    { label: 'Create Referral' },
  ],
  '/incident-reporting': [{ label: 'Incident Reporting' }],
  '/discounts': [{ label: 'Discounts' }],
  '/discounts/discount-code': [
    { label: 'Discounts', href: '/discounts' },
    { label: 'Discount Codes' },
  ],
  '/transactions': [{ label: 'Transactions' }],
  '/manage-staffs': [{ label: 'Manage Staffs' }],
  '/manage-staffs/create': [
    { label: 'Manage Staffs', href: '/manage-staffs' },
    { label: 'Add Staff' },
  ],
  '/manage-admin': [{ label: 'Manage Admin' }],
  '/manage-roles': [{ label: 'Manage Roles' }],
  '/rewards': [{ label: 'Rewards' }],
  '/locations': [{ label: 'Locations' }],
  '/forum': [{ label: 'Forum' }],
  '/forum/create': [
    { label: 'Forum', href: '/forum' },
    { label: 'Create Post' },
  ],
  '/innovation-hub': [{ label: 'Innovation Hub' }],
  '/innovation-hub/create': [
    { label: 'Innovation Hub', href: '/innovation-hub' },
    { label: 'Submit Idea' },
  ],
  '/process-dictionary': [{ label: 'Process Dictionary' }],
  '/chis': [{ label: 'CHIS' }],
  '/ai-assistant': [{ label: 'AI Assistant' }],
  '/system-settings': [{ label: 'System Settings' }],
  '/profile': [{ label: 'Profile' }],
  '/surveys': [{ label: 'Surveys' }],
  '/surveys/create': [
    { label: 'Surveys', href: '/surveys' },
    { label: 'Create Survey' },
  ],
  '/health-insurance-scheme': [{ label: 'Health Insurance' }],
  '/patient-management': [{ label: 'Patient Management' }],
  '/vmi': [{ label: 'VMI' }],
  '/data-analysis': [{ label: 'Data Analysis' }],
  '/games': [{ label: 'Games' }],
};

export default function Breadcrumb({ data }: BreadcrumbProps) {
  const pathname = usePathname();

  // Handle dynamic routes by checking patterns
  const getDynamicBreadcrumbs = (path: string): BreadcrumbItem[] | null => {
    if (path.startsWith('/packages/edit-package/')) {
      return [
        { label: 'Packages', href: '/packages' },
        { label: 'Manage Packages', href: '/packages/manage-package' },
        { label: 'Edit Package' },
      ];
    }
    if (path.match(/\/manage-staffs\/\d+/)) {
      return [
        { label: 'Manage Staffs', href: '/manage-staffs' },
        { label: 'Edit Staff' },
      ];
    }
    if (path.match(/\/forum\/\d+/)) {
      return [{ label: 'Forum', href: '/forum' }, { label: 'View Post' }];
    }
    if (path.match(/\/innovation-hub\/\d+/)) {
      return [
        { label: 'Innovation Hub', href: '/innovation-hub' },
        { label: 'View Idea' },
      ];
    }
    if (path.match(/\/surveys\/\d+/)) {
      return [{ label: 'Surveys', href: '/surveys' }, { label: 'View Survey' }];
    }
    if (path.match(/\/patient-management\/\d+/)) {
      return [
        { label: 'Patient Management', href: '/patient-management' },
        { label: 'Patient Details' },
      ];
    }
    return null;
  };

  const breadcrumbs =
    data || pathToBreadcrumb[pathname] || getDynamicBreadcrumbs(pathname);

  if (!breadcrumbs || breadcrumbs.length === 0) {
    return null;
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 overflow-hidden">
      <Link
        href="/dashboard"
        className="flex items-center hover:text-gray-900 dark:hover:text-gray-200 transition-colors flex-shrink-0"
      >
        <Home className="h-4 w-4" />
      </Link>

      {breadcrumbs.map((item, index) => (
        <div key={index} className="flex items-center space-x-1 min-w-0">
          <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
          {item.href ? (
            <Link
              href={item.href}
              className="hover:text-gray-900 dark:hover:text-gray-200 transition-colors truncate max-w-24 sm:max-w-32 md:max-w-none"
              title={item.label}
            >
              {item.label}
            </Link>
          ) : (
            <span
              className="text-gray-900 dark:text-gray-200 font-medium truncate max-w-24 sm:max-w-32 md:max-w-none"
              title={item.label}
            >
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  );
}
