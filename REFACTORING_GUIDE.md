# Data Table Refactoring Guide: URL-Based Filtering

This guide explains how to refactor data-table components from using `useEffect` for parameter management to using `useSearchParams` from Next.js navigation for URL-based filtering.

## Benefits of URL-Based Filtering

1. **Shareable URLs**: Users can share filtered views with others
2. **Browser Navigation**: Back/forward buttons work correctly with filters
3. **Bookmarkable**: Users can bookmark specific filtered states
4. **SEO Friendly**: Search engines can index different filtered views
5. **Better UX**: Page refreshes maintain filter state

## Migration Steps

### Step 1: Replace the Hook Import

**Before:**
```typescript
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
```

**After:**
```typescript
import { useUrlSearchAndPagination } from '@/hooks/useUrlSearchAndPagination';
// OR for more advanced filtering:
import { useDataTableFilters, commonFilterConfigs } from '@/hooks/useDataTableFilters';
```

### Step 2: Update Hook Usage

**Before:**
```typescript
const {
  searchTerm,
  debouncedSearchTerm,
  handleSearchChange,
  currentPage,
  pageSize,
  handlePageChange,
  queryParam,
  setQueryParam,
} = useSearchAndPagination({ initialPageSize: 10 });

const [statusFilter, setStatusFilter] = useState('');
const [startDate, setStartDate] = useState<Date | undefined>(undefined);
const [endDate, setEndDate] = useState<Date | undefined>(undefined);
```

**After (Simple approach):**
```typescript
const {
  searchTerm,
  debouncedSearchTerm,
  handleSearchChange,
  currentPage,
  pageSize,
  handlePageChange,
  updateUrlParams,
  getUrlParam,
  buildApiQuery,
} = useUrlSearchAndPagination({ initialPageSize: 10 });

// Initialize from URL parameters
const [statusFilter, setStatusFilter] = useState(getUrlParam('status') || '');
const [startDate, setStartDate] = useState<Date | undefined>(
  getUrlParam('startDate') ? new Date(getUrlParam('startDate')!) : undefined
);
const [endDate, setEndDate] = useState<Date | undefined>(
  getUrlParam('endDate') ? new Date(getUrlParam('endDate')!) : undefined
);
```

**After (Advanced approach with useDataTableFilters):**
```typescript
const filterConfig = {
  ...commonFilterConfigs.dateRange,
  ...commonFilterConfigs.status,
  ...commonFilterConfigs.type,
};

const {
  searchTerm,
  handleSearchChange,
  currentPage,
  pageSize,
  handlePageChange,
  filters,
  updateFilter,
  buildApiQuery,
} = useDataTableFilters({
  initialPageSize: 10,
  filterConfig,
});

// Extract filter values
const statusFilter = filters.status || '';
const startDate = filters.startDate;
const endDate = filters.endDate;
```

### Step 3: Replace useEffect with URL Updates

**Before:**
```typescript
useEffect(() => {
  let params = [];

  if (statusFilter && statusFilter !== 'all') {
    params.push(`status=${statusFilter}`);
  }

  if (startDate) {
    const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
    params.push(`startDate=${formattedStartDate}`);
  }

  if (endDate) {
    const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
    params.push(`endDate=${formattedEndDate}`);
  }

  setQueryParam(params.join('&'));
}, [statusFilter, startDate, endDate, setQueryParam]);
```

**After (Simple approach):**
```typescript
useEffect(() => {
  const params: Record<string, string | null> = {
    status: statusFilter && statusFilter !== 'all' ? statusFilter : null,
    startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : null,
    endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : null,
  };

  updateUrlParams(params);
}, [statusFilter, startDate, endDate, updateUrlParams]);
```

**After (Advanced approach):**
```typescript
// No useEffect needed! The hook handles URL updates automatically
```

### Step 4: Update API Calls

**Before:**
```typescript
const { data, loading } = YourApiHook(
  `?page=${currentPage}&limit=${pageSize}&${queryParam}`
);
```

**After:**
```typescript
const apiQuery = buildApiQuery();
const { data, loading } = YourApiHook(`?${apiQuery}`);
```

### Step 5: Update Filter Handlers

**Before:**
```typescript
const handleStatusChange = (value: string) => {
  setStatusFilter(value);
};
```

**After (Simple approach):**
```typescript
const handleStatusChange = (value: string) => {
  setStatusFilter(value);
  // URL update happens in useEffect
};
```

**After (Advanced approach):**
```typescript
const handleStatusChange = (value: string) => {
  updateFilter('status', value);
  // URL update happens automatically
};
```

## Complete Example

Here's a complete before/after comparison:

### Before (useEffect approach):
```typescript
const DataTable = () => {
  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);

  useEffect(() => {
    let params = [];
    if (statusFilter) params.push(`status=${statusFilter}`);
    if (startDate) params.push(`startDate=${dayjs(startDate).format('YYYY-MM-DD')}`);
    setQueryParam(params.join('&'));
  }, [statusFilter, startDate, setQueryParam]);

  const { data } = YourApiHook(`?page=${currentPage}&${queryParam}`);

  return (
    <div>
      <Select value={statusFilter} onValueChange={setStatusFilter}>
        {/* options */}
      </Select>
      {/* table */}
    </div>
  );
};
```

### After (URL-based approach):
```typescript
const DataTable = () => {
  const filterConfig = {
    ...commonFilterConfigs.dateRange,
    ...commonFilterConfigs.status,
  };

  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    handlePageChange,
    filters,
    updateFilter,
    buildApiQuery,
  } = useDataTableFilters({
    initialPageSize: 10,
    filterConfig,
  });

  const statusFilter = filters.status || '';
  const startDate = filters.startDate;

  const apiQuery = buildApiQuery();
  const { data } = YourApiHook(`?${apiQuery}`);

  return (
    <div>
      <Select 
        value={statusFilter} 
        onValueChange={(value) => updateFilter('status', value)}
      >
        {/* options */}
      </Select>
      {/* table */}
    </div>
  );
};
```

## Components to Refactor

The following components should be refactored to use URL-based filtering:

1. `src/components/transactions/components/data-table.tsx` ✅ (Already done)
2. `src/components/feedbacks/components/data-table.tsx` ✅ (Already done)
3. `src/components/packages/booking/components/data-table.tsx`
4. `src/components/discounts/components/data-table.tsx`
5. `src/components/patient-management/patients/components/data-table.tsx`
6. `src/components/patient-management/appointments/components/data-table.tsx`
7. `src/components/patient-management/medical-records/components/data-table.tsx`
8. `src/components/referrals/referral/data-table.tsx`
9. `src/components/innovation-hub/index.tsx`

## Testing

After refactoring, test the following:

1. **URL Updates**: Verify that changing filters updates the URL
2. **URL Sharing**: Copy a filtered URL and open in new tab - filters should be applied
3. **Browser Navigation**: Use back/forward buttons - filter state should be maintained
4. **Page Refresh**: Refresh the page - filters should persist
5. **API Calls**: Verify that API calls include correct parameters from URL

## Notes

- The new hooks are backward compatible with existing DateRangeFilter components
- URLs will be cleaner and more semantic
- Filter state is automatically synchronized between URL and component state
- The approach scales well for complex filtering scenarios
