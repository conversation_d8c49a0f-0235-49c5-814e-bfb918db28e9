'use client';

interface PaginationProps {
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  totalPages,
  currentPage,
  onPageChange,
}) => {
  let pageNumbers: (number | string)[] = [];

  const handleFirstPageButtonClick = () => {
    onPageChange(1);
  };

  const handlePreviousPageButtonClick = () => {
    onPageChange(currentPage - 1);
  };

  const handleNextPageButtonClick = () => {
    onPageChange(currentPage + 1);
  };

  const handleLastPageButtonClick = () => {
    onPageChange(totalPages);
  };

  const handleNumberButtonClick = (pageNumber: number) => {
    onPageChange(pageNumber);
  };

  if (totalPages <= 5) {
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i);
    }
  } else if (currentPage <= 3) {
    pageNumbers = [1, 2, 3, 4, '...', totalPages];
  } else if (currentPage >= totalPages - 2) {
    pageNumbers = [
      1,
      '...',
      totalPages - 3,
      totalPages - 2,
      totalPages - 1,
      totalPages,
    ];
  } else {
    pageNumbers = [
      1,
      '...',
      currentPage - 1,
      currentPage,
      currentPage + 1,
      '...',
      totalPages,
    ];
  }

  return (
    <div className="w-full py-2 items-center mx-auto flex justify-between">
      <div className="mt-4 flex w-full justify-end space-x-2">
        {/* First Page Button */}
        <button
          className={`${
            currentPage === 1
              ? 'cursor-not-allowed rounded-[3px] bg-gray-400 text-white'
              : 'cursor-pointer rounded-[3px] bg-primary text-gray-300 dark:text-gray-800'
          } inline-flex h-6 w-6 items-center justify-center leading-none`}
          onClick={handleFirstPageButtonClick}
          disabled={currentPage === 1}
        >
          <svg
            className="w-4"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="11 17 6 12 11 7"></polyline>
            <polyline points="17 17 12 12 17 7"></polyline>
          </svg>
        </button>

        {/* Previous Page Button */}
        <button
          className={`${
            currentPage === 1
              ? 'cursor-not-allowed rounded-[3px] bg-gray-400 text-white'
              : 'cursor-pointer rounded-[3px] bg-primary text-gray-300 dark:text-gray-800'
          } inline-flex h-6 w-6 items-center justify-center leading-none`}
          onClick={handlePreviousPageButtonClick}
          disabled={currentPage === 1}
        >
          <svg
            className="w-4"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>

        {/* Page Number Buttons */}
        {pageNumbers.map((number, index) =>
          number === '...' ? (
            <span key={`ellipsis-${index}`}>{number}</span>
          ) : (
            <button
              className={`${
                currentPage === number
                  ? 'cursor-not-allowed bg-primary text-white dark:text-gray-800'
                  : 'cursor-pointer text-gray-600 dark:text-white'
              } cur inline-flex h-6 w-6 items-center justify-center rounded-[2px] text-xs leading-none hover:border hover:border-border`}
              key={`page-${number}`}
              onClick={() => handleNumberButtonClick(number as number)}
              disabled={currentPage === number}
            >
              {number}
            </button>
          )
        )}

        {/* Next Page Button */}
        {/* Next Page Button */}
        <button
          className={`${
            currentPage === totalPages
              ? 'cursor-not-allowed rounded-[3px] bg-gray-400 text-white dark:text-gray-800'
              : 'cursor-pointer rounded-[3px] bg-primary text-gray-300 dark:text-gray-800 hover:border-gray-200 hover:shadow hover:dark:border-gray-600'
          } inline-flex h-6 w-6 items-center justify-center leading-none`}
          onClick={handleNextPageButtonClick}
          disabled={currentPage === totalPages}
        >
          <svg
            className="w-4"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </button>

        {/* Last Page Button */}
        <button
          className={`${
            currentPage === totalPages
              ? 'cursor-not-allowed rounded-[3px] bg-gray-400 text-white dark:text-gray-800'
              : 'cursor-pointer rounded-[3px] bg-primary text-gray-300 dark:text-gray-800 hover:border-gray-200 hover:shadow hover:dark:border-gray-600'
          } inline-flex h-6 w-6 items-center justify-center leading-none`}
          onClick={handleLastPageButtonClick}
          disabled={currentPage === totalPages}
        >
          <svg
            className="w-4"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="13 17 18 12 13 7"></polyline>
            <polyline points="7 17 12 12 7 7"></polyline>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Pagination;
