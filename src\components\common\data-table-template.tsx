'use client';

import React, { useState } from 'react';
import { useDataTableFilters, commonFilterConfigs } from '@/hooks/useDataTableFilters';
import Pagination from '@/components/common/pagination';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import DateRangeFilter from '@/components/common/date-range-filter';

/**
 * Template for data-table components using URL-based filtering
 * 
 * This template demonstrates how to:
 * 1. Use useDataTableFilters hook for URL-based state management
 * 2. Initialize filters from URL parameters
 * 3. Update URL when filters change
 * 4. Build API queries from URL parameters
 * 5. Make URLs shareable
 */

interface DataTableTemplateProps {
  // Add your specific props here
  title?: string;
}

const DataTableTemplate: React.FC<DataTableTemplateProps> = ({ title = "Data Table" }) => {
  // Configure filters for this specific table
  const filterConfig = {
    ...commonFilterConfigs.dateRange,
    ...commonFilterConfigs.status,
    ...commonFilterConfigs.type,
    // Add more filters as needed
  };

  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    filters,
    updateFilter,
    clearFilters,
    buildApiQuery,
  } = useDataTableFilters({
    initialPageSize: 10,
    filterConfig,
  });

  // Local state for modals, etc.
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Extract filter values for easier access
  const statusFilter = filters.status || '';
  const typeFilter = filters.type || '';
  const startDate = filters.startDate;
  const endDate = filters.endDate;

  // Build API query and fetch data
  const apiQuery = buildApiQuery();
  
  // Replace this with your actual API call
  // const { data, loading, mutate } = YourApiHook(`?${apiQuery}`);
  const data = []; // Mock data
  const loading = false;
  const totalPages = 1;

  // Filter handlers
  const handleStatusChange = (value: string) => {
    updateFilter('status', value);
  };

  const handleTypeChange = (value: string) => {
    updateFilter('type', value);
  };

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    updateFilter('startDate', start);
    updateFilter('endDate', end);
  };

  const handleClearFilters = () => {
    clearFilters(['search']); // Keep search, clear other filters
  };

  return (
    <div className="w-full space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{title}</h1>
        <Button onClick={handleClearFilters} variant="outline" size="sm">
          Clear Filters
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        {/* Search */}
        <div className="relative min-w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-500" />
          </div>
          <Input
            type="text"
            placeholder="Search..."
            className="pl-10"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>

        {/* Status Filter */}
        <Select value={statusFilter} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>

        {/* Type Filter */}
        <Select value={typeFilter} onValueChange={handleTypeChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Types</SelectItem>
            <SelectItem value="type1">Type 1</SelectItem>
            <SelectItem value="type2">Type 2</SelectItem>
          </SelectContent>
        </Select>

        {/* Date Range Filter */}
        <DateRangeFilter
          startDate={startDate}
          endDate={endDate}
          onStartDateChange={(date) => updateFilter('startDate', date)}
          onEndDateChange={(date) => updateFilter('endDate', date)}
          className="w-[250px]"
        />
      </div>

      {/* Table */}
      <div className="w-full overflow-x-auto rounded-lg shadow-md">
        <table className="w-full bg-white dark:bg-gray-800">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Column 1
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Column 2
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {loading ? (
              <tr>
                <td colSpan={3} className="px-6 py-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={3} className="px-6 py-4 text-center text-gray-500">
                  No data found
                </td>
              </tr>
            ) : (
              data.map((item: any, index: number) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    {/* Your data here */}
                    Sample Data {index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {/* Your data here */}
                    More Data
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedItem(item);
                        setShowDetails(true);
                      }}
                    >
                      View Details
                    </Button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      )}

      {/* Debug Info (remove in production) */}
      <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <p><strong>API Query:</strong> {apiQuery}</p>
        <p><strong>Current URL would be:</strong> {typeof window !== 'undefined' ? `${window.location.pathname}?${apiQuery}` : 'N/A'}</p>
        <p><strong>Filters:</strong> {JSON.stringify(filters, null, 2)}</p>
      </div>
    </div>
  );
};

export default DataTableTemplate;
