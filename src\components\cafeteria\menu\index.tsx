'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { MenuSquare, Plus, Search, Edit, Trash, Eye } from 'lucide-react';
import { GetProfile } from '@/api/staff';
import dayjs from 'dayjs';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import { GetAllMenu } from '@/api/cafeteria/menu';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import Pagination from '@/components/common/pagination';
import AddMenu from './components/add-menu';
import { numberFormat } from '@/lib/utils';
import Details from './components/detail';

export default function MenuManagement() {
  const { profile } = GetProfile();
  const [openCreateMenu, setOpenCreateMenu] = useState(false);
  const [detail, setDetail] = useState<any | null>(null);
  const [openDetail, setOpenDetail] = useState(false);

  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 15 });

  const { menu, menuLoading, mutate } = GetAllMenu(
    `?${buildApiQuery()}`
  );
  const menuData = menu?.data?.menu;
  const totalPages = menu?.data?.totalPages ?? 0;

  const canManage = hasPermission(PERMISSIONS.CAFETERIA_MENU_MANAGE);

  const handleEventFromModal = (menu: any) => {
    setDetail(menu);
    setOpenDetail(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search ..."
            className="pl-10 pr-4 py-2 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <Button
          onClick={() => setOpenCreateMenu(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" /> Add Menu Item
        </Button>
      </div>
      <div className="border rounded-md p-2">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Menu Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>General Price</TableHead>
              <TableHead>Staff Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {menuLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  <LoadingState />
                </TableCell>
              </TableRow>
            ) : menuData.length > 0 ? (
              menuData.map((item: any, index: number) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    {' '}
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell>{item.menuCategory.name}</TableCell>
                  <TableCell>{numberFormat(item.generalPrice)}</TableCell>
                  <TableCell>{numberFormat(item.staffPrice)}</TableCell>
                  <TableCell>
                    <Badge
                      variant={item.isAvailable ? 'success' : 'destructive'}
                    >
                      {item.isAvailable ? 'Available' : 'Unavailable'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        onClick={() => handleEventFromModal(item)}
                        variant="ghost"
                        size="icon"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  <EmptyState />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}

      <AddMenu
        open={openCreateMenu}
        setOpen={setOpenCreateMenu}
        mutate={mutate}
        profile={profile?.data}
      />
      <Details
        open={openDetail}
        setOpen={setOpenDetail}
        mutate={mutate}
        data={detail}
      />
    </div>
  );
}
