import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '../types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  Loader2,
  User,
  Co<PERSON>,
  ClipboardCheck,
  Edit2,
  Check,
  X,
} from 'lucide-react';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { StatusBadge } from '@/components/common/status-badge';
import { numberFormat, staffType, formatRoleNames } from '@/lib/utils';

// Tab interface
interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState({
    fullName: '',
    email: '',
    type: '',
    creditLimit: 0,
  });

  // Reset active tab and edit values when modal opens
  useEffect(() => {
    if (open && data) {
      setActiveTab(0);
      setEditingField(null);
      setEditValues({
        fullName: data.fullName || '',
        email: data.email || '',
        type: data.type || '',
        creditLimit: data.creditLimit || 0,
      });
    }
  }, [open, data]);

  // Early return if no data
  if (!data) {
    return null;
  }

  // Function to toggle staff active status
  const handleToggleStatus = async () => {
    if (!data?.id) {
      toast.error('Staff ID is missing');
      return;
    }

    try {
      setIsLoading(true);
      const res = await myApi.patch('/staff/update-admin', {
        id: data.id,
        isActive: !data.isActive,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message || 'Staff status updated successfully');
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to update staff status');
    }
  };

  // Function to save edited field
  const handleSaveField = async (field: string) => {
    if (!data?.id) {
      toast.error('Staff ID is missing');
      return;
    }

    try {
      setIsLoading(true);
      const updateData: any = { id: data.id };
      updateData[field] = editValues[field as keyof typeof editValues];

      const res = await myApi.patch('/staff/update-admin', updateData);

      if (res.status === 200) {
        toast.success('Staff updated successfully');
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      toast.error('Failed to update staff');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to cancel editing
  const handleCancelEdit = (field: string) => {
    setEditingField(null);
    setEditValues((prev) => ({
      ...prev,
      [field]: data?.[field as keyof typeof data] || '',
    }));
  };

  // Render editable field
  const renderEditableField = (
    label: string,
    field: string,
    value: any,
    type: 'text' | 'email' | 'number' | 'select' = 'text'
  ) => {
    const isEditing = editingField === field;

    return (
      <div key={field}>
        <div className="flex">
          <p className="text-sm font-medium">{label}</p>
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingField(field)}
              className="h-4 w-4 ml-4"
            >
              <Edit2 className="w-3 h-3" />
            </Button>
          )}
        </div>

        {isEditing ? (
          <div className="flex items-center gap-2 mt-1">
            {type === 'select' && field === 'type' ? (
              <Select
                value={editValues[field as keyof typeof editValues] as string}
                onValueChange={(value) =>
                  setEditValues((prev) => ({ ...prev, [field]: value }))
                }
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {staffType.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                type={type}
                value={editValues[field as keyof typeof editValues]}
                onChange={(e) =>
                  setEditValues((prev) => ({
                    ...prev,
                    [field]:
                      type === 'number'
                        ? Number(e.target.value)
                        : e.target.value,
                  }))
                }
                className="h-8 text-sm"
                autoFocus
              />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSaveField(field)}
              disabled={isLoading}
              className="h-6 w-6 p-0"
            >
              <Check className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCancelEdit(field)}
              className="h-6 w-6 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {value || 'N/A'}
          </p>
        )}
      </div>
    );
  };

  // Personal Information Tab Content
  const PersonalInfo = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Personal Details Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Personal Details
        </h3>

        {renderEditableField('Full Name', 'fullName', data?.fullName, 'text')}

        {renderEditableField('Email', 'email', data?.email, 'email')}

        <div>
          <p className="text-sm font-medium">Phone Number</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.phoneNumber || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Staff ID</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.staffCode || data?.staffCode || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Status</p>
          <div className="mt-1">
            {data?.isActive !== undefined
              ? data.isActive
                ? StatusBadge({ status: 'active' })
                : StatusBadge({ status: 'inactive' })
              : 'N/A'}
          </div>
        </div>
      </div>

      {/* Professional Details Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Professional Details
        </h3>

        <div>
          <p className="text-sm font-medium">Location</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.location?.name && data?.location?.region?.name
              ? `${data.location.name}, ${data.location.region.name}`
              : data?.location?.name && data?.location?.region
                ? `${data.location.name}, ${data.location.region}`
                : data?.location?.name || 'N/A'}
          </p>
        </div>

        {renderEditableField('Staff Type', 'type', data?.type, 'select')}

        <div>
          <p className="text-sm font-medium">Role Permissions</p>
          <div className="mt-1">
            <Badge variant="outline" className="w-fit">
              {data?.roles ? formatRoleNames(data.roles) : 'N/A'}
            </Badge>
          </div>
        </div>

        <div>
          <p className="text-sm font-medium">Department</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.department?.name || 'N/A'}
          </p>
        </div>

        {data?.unitId && data?.unit && (
          <div>
            <p className="text-sm font-medium">Unit</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.unit.name}
            </p>
          </div>
        )}

        {data?.doctorProfile && (
          <>
            <div>
              <p className="text-sm font-medium">Is Consultant?</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.doctorProfile.isConsultant
                  ? data.doctorProfile.isVisitingConsultant
                    ? 'Visiting Consultant'
                    : 'Consultant'
                  : 'Doctor'}
              </p>
            </div>
            {data?.doctorProfile?.specialty && (
              <div>
                <p className="text-sm font-medium">Specialty</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.doctorProfile?.specialty?.name}
                </p>
              </div>
            )}
          </>
        )}

        <div>
          <p className="text-sm font-medium">Date Created</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.createdAt
              ? dayjs(data.createdAt).format('MMMM D, YYYY')
              : 'N/A'}
          </p>
        </div>
      </div>
    </div>
  );

  // Referral Information Tab Content
  const ReferralInfo = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Referral Information Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Referral Information
        </h3>

        <div>
          <p className="text-sm font-medium">Referral Code</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="px-3 py-1 text-sm">
              {data?.referralCode?.code || 'N/A'}
            </Badge>
            {data?.referralCode?.code && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(data.referralCode?.code || '');
                  toast.success('Referral code copied to clipboard');
                }}
              >
                <Copy className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>

        <div>
          <p className="text-sm font-medium">Code Usage</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.codeUsage || '0'} times
          </p>
        </div>
      </div>

      {/* Financial Information Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Financial Information
        </h3>

        <div>
          <p className="text-sm font-medium">Wallet Balance</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.wallet ? numberFormat(data.wallet) : '₦0.00'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Meal Voucher</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.mealVoucher ? numberFormat(data?.mealVoucher) : '₦0.00'}
          </p>
        </div>

        {data?.creditLimit !== undefined &&
          renderEditableField(
            'Credit Limit',
            'creditLimit',
            numberFormat(data.creditLimit),
            'number'
          )}

        {data?.monthlyCreditUsed !== undefined && (
          <div>
            <p className="text-sm font-medium">Monthly Credit Used</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {numberFormat(data.monthlyCreditUsed)}
            </p>
          </div>
        )}

        <div>
          <p className="text-sm font-medium">Total Earnings</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.total
              ? numberFormat(data.total)
              : '₦0.00'}
          </p>
        </div>
      </div>
    </div>
  );

  const tabs: TabProps[] = [
    {
      label: 'Personal & Professional',
      icon: <User className="w-4 h-4" />,
      content: <PersonalInfo />,
    },
    {
      label: 'Referral & Financial',
      icon: <ClipboardCheck className="w-4 h-4" />,
      content: <ReferralInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Staff Details"
      description={`Comprehensive information for ${data?.fullName || 'Unknown Staff'}`}
      size="lg"
    >
      {/* Tabs */}
      <div className="flex border-b mb-4">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={cn(
              'flex items-center px-2 md:px-4 py-2 text-sm font-medium transition-colors',
              activeTab === index
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            )}
            onClick={() => setActiveTab(index)}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab content */}
      <div className="py-2 mb-6">{tabs[activeTab].content}</div>

      {/* Action buttons */}
      <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
        <Button variant="outline" onClick={() => setOpen(false)}>
          Close
        </Button>
        <Button
          variant={data?.isActive ? 'destructive' : 'default'}
          onClick={handleToggleStatus}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
          {data?.isActive ? 'Deactivate Staff' : 'Activate Staff'}
        </Button>
      </div>
    </Modal>
  );
};

export default Details;
