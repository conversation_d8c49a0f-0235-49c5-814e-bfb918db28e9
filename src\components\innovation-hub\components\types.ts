import * as z from 'zod';

// // Schema for the idea submission form
// export const ideaSchema = z.object({
//   title: z.string().min(5, {
//     message: 'Title must be at least 5 characters long.',
//   }),
//   description: z.string().min(20, {
//     message: 'Description must be at least 20 characters long.',
//   }),
//   categoryId: z.string({
//     required_error: 'Please select a category.',
//   }),
//   tagIds: z.array(z.string()).min(1, {
//     message: 'Please select at least one tag.',
//   }),
// });

// export type IdeaFormValues = z.infer<typeof ideaSchema>;

// // Schema for the comment submission form
// export const commentSchema = z.object({
//   comment: z
//     .string()
//     .min(1, { message: 'Comment cannot be empty.' })
//     .max(150, { message: 'Each comment cannot be more than 150 characters.' }),
// });
// export type CommentFormValues = z.infer<typeof commentSchema>;

export const ideaStatuses = [
  'PENDING_REVIEW',
  'ACCEPTED',
  'REJECTED',
  'IMPLEMENTED',
] as const;
