'use client';

import { useCallback } from 'react';
import { useUrlFilters } from './useUrlFilters';
import dayjs from 'dayjs';

interface FilterConfig {
  [key: string]: {
    defaultValue?: string | Date | null;
    formatter?: (value: any) => string | null;
    parser?: (value: string) => any;
  };
}

interface UseDataTableFiltersProps {
  initialPageSize?: number;
  debounceDelay?: number;
  filterConfig?: FilterConfig;
}

interface UseDataTableFiltersReturn {
  // Search and pagination
  searchTerm: string;
  debouncedSearchTerm: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  currentPage: number;
  pageSize: number;
  handlePageChange: (pageNumber: number) => void;
  
  // Filter management
  filters: Record<string, any>;
  updateFilter: (key: string, value: any) => void;
  clearFilters: (keysToKeep?: string[]) => void;
  
  // API query building
  buildApiQuery: () => string;
  
  // URL management
  getUrlParam: (key: string) => string | null;
}

export function useDataTableFilters({
  initialPageSize = 15,
  debounceDelay = 500,
  filterConfig = {},
}: UseDataTableFiltersProps = {}): UseDataTableFiltersReturn {
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    getFilter,
    setFilter,
    setFilters,
    clearFilters: baseClearFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize, debounceDelay });

  // Get current filter values from URL
  const filters: Record<string, any> = {};
  Object.keys(filterConfig).forEach(key => {
    const urlValue = getFilter(key);
    const config = filterConfig[key];
    if (urlValue) {
      filters[key] = config.parser ? config.parser(urlValue) : urlValue;
    } else {
      filters[key] = config.defaultValue || null;
    }
  });

  // Update filter value
  const updateFilter = useCallback((key: string, value: any) => {
    const config = filterConfig[key];
    if (value !== null && value !== undefined && value !== '') {
      if (config?.formatter) {
        setFilter(key, config.formatter(value));
      } else if (value instanceof Date) {
        setFilter(key, dayjs(value).format('YYYY-MM-DD'));
      } else {
        setFilter(key, String(value));
      }
    } else {
      setFilter(key, null);
    }
  }, [filterConfig, setFilter]);

  // Clear filters
  const clearFilters = useCallback((keysToKeep: string[] = []) => {
    baseClearFilters([...keysToKeep, 'search', 'page']);
  }, [baseClearFilters]);

  return {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    filters,
    updateFilter,
    clearFilters,
    buildApiQuery,
    getUrlParam: getFilter,
  };
}

// Common filter configurations
export const commonFilterConfigs = {
  dateRange: {
    startDate: {
      defaultValue: null,
      formatter: (date: Date) => date ? dayjs(date).format('YYYY-MM-DD') : null,
      parser: (value: string) => new Date(value),
    },
    endDate: {
      defaultValue: null,
      formatter: (date: Date) => date ? dayjs(date).format('YYYY-MM-DD') : null,
      parser: (value: string) => new Date(value),
    },
  },
  status: {
    status: {
      defaultValue: '',
      formatter: (value: string) => value && value !== 'all' ? value : null,
    },
  },
  rating: {
    rating: {
      defaultValue: '',
      formatter: (value: string) => value && value !== 'all' ? value : null,
    },
  },
  type: {
    type: {
      defaultValue: '',
      formatter: (value: string) => value || null,
    },
  },
  viewType: {
    viewType: {
      defaultValue: 'my',
    },
  },
};
