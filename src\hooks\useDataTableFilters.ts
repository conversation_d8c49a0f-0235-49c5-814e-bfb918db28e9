'use client';

import { useState, useEffect, useCallback } from 'react';
import { useUrlSearchAndPagination } from './useUrlSearchAndPagination';
import dayjs from 'dayjs';

interface FilterConfig {
  [key: string]: {
    defaultValue?: string | Date | null;
    formatter?: (value: any) => string | null;
    parser?: (value: string) => any;
  };
}

interface UseDataTableFiltersProps {
  initialPageSize?: number;
  debounceDelay?: number;
  filterConfig?: FilterConfig;
}

interface UseDataTableFiltersReturn {
  // Search and pagination
  searchTerm: string;
  debouncedSearchTerm: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  currentPage: number;
  pageSize: number;
  handlePageChange: (pageNumber: number) => void;
  
  // Filter management
  filters: Record<string, any>;
  updateFilter: (key: string, value: any) => void;
  clearFilters: (keysToKeep?: string[]) => void;
  
  // API query building
  buildApiQuery: () => string;
  
  // URL management
  getUrlParam: (key: string) => string | null;
}

export function useDataTableFilters({
  initialPageSize = 15,
  debounceDelay = 500,
  filterConfig = {},
}: UseDataTableFiltersProps = {}): UseDataTableFiltersReturn {
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    updateUrlParams,
    getUrlParam,
    buildApiQuery: baseBuildApiQuery,
  } = useUrlSearchAndPagination({ initialPageSize, debounceDelay });

  // Initialize filters from URL parameters
  const [filters, setFilters] = useState<Record<string, any>>(() => {
    const initialFilters: Record<string, any> = {};
    
    Object.entries(filterConfig).forEach(([key, config]) => {
      const urlValue = getUrlParam(key);
      if (urlValue) {
        initialFilters[key] = config.parser ? config.parser(urlValue) : urlValue;
      } else {
        initialFilters[key] = config.defaultValue || null;
      }
    });
    
    return initialFilters;
  });

  // Update filter value
  const updateFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  // Clear filters
  const clearFilters = useCallback((keysToKeep: string[] = []) => {
    const clearedFilters: Record<string, any> = {};
    
    keysToKeep.forEach(key => {
      clearedFilters[key] = filters[key];
    });
    
    Object.keys(filterConfig).forEach(key => {
      if (!keysToKeep.includes(key)) {
        clearedFilters[key] = filterConfig[key].defaultValue || null;
      }
    });
    
    setFilters(clearedFilters);
  }, [filters, filterConfig]);

  // Update URL parameters when filters change
  useEffect(() => {
    const params: Record<string, string | null> = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      const config = filterConfig[key];
      if (value !== null && value !== undefined && value !== '') {
        if (config?.formatter) {
          params[key] = config.formatter(value);
        } else if (value instanceof Date) {
          params[key] = dayjs(value).format('YYYY-MM-DD');
        } else {
          params[key] = String(value);
        }
      } else {
        params[key] = null;
      }
    });

    updateUrlParams(params);
  }, [filters, filterConfig, updateUrlParams]);

  // Enhanced API query builder that includes filters
  const buildApiQuery = useCallback(() => {
    return baseBuildApiQuery();
  }, [baseBuildApiQuery]);

  return {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    filters,
    updateFilter,
    clearFilters,
    buildApiQuery,
    getUrlParam,
  };
}

// Common filter configurations
export const commonFilterConfigs = {
  dateRange: {
    startDate: {
      defaultValue: null,
      formatter: (date: Date) => date ? dayjs(date).format('YYYY-MM-DD') : null,
      parser: (value: string) => new Date(value),
    },
    endDate: {
      defaultValue: null,
      formatter: (date: Date) => date ? dayjs(date).format('YYYY-MM-DD') : null,
      parser: (value: string) => new Date(value),
    },
  },
  status: {
    status: {
      defaultValue: '',
      formatter: (value: string) => value && value !== 'all' ? value : null,
    },
  },
  rating: {
    rating: {
      defaultValue: '',
      formatter: (value: string) => value && value !== 'all' ? value : null,
    },
  },
  type: {
    type: {
      defaultValue: '',
      formatter: (value: string) => value || null,
    },
  },
  viewType: {
    viewType: {
      defaultValue: 'my',
    },
  },
};
