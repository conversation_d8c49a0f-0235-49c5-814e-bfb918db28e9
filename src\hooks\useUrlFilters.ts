'use client';

import { useCallback, useMemo } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useDebounce } from './useDebounce';
import dayjs from 'dayjs';

interface UseUrlFiltersProps {
  initialPageSize?: number;
  debounceDelay?: number;
}

interface UseUrlFiltersReturn {
  // Search and pagination
  searchTerm: string;
  debouncedSearchTerm: string;
  currentPage: number;
  pageSize: number;
  
  // URL-based filter getters (no useState needed!)
  getFilter: (key: string) => string | null;
  getDateFilter: (key: string) => Date | null;
  getBooleanFilter: (key: string) => boolean;
  
  // URL-based filter setters (directly update URL)
  setFilter: (key: string, value: string | null) => void;
  setDateFilter: (key: string, value: Date | null) => void;
  setFilters: (filters: Record<string, string | Date | null>) => void;
  clearFilters: (keysToKeep?: string[]) => void;
  
  // Actions
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePageChange: (pageNumber: number) => void;
  
  // API query building
  buildApiQuery: () => string;
}

export function useUrlFilters({
  initialPageSize = 15,
  debounceDelay = 500,
}: UseUrlFiltersProps = {}): UseUrlFiltersReturn {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  
  // Get current values directly from URL (no useState!)
  const searchTerm = searchParams.get('search') || '';
  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = initialPageSize;
  
  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, debounceDelay);

  // Helper to update URL parameters
  const updateUrl = useCallback((params: Record<string, string | null>) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    // Update or remove parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === '' || value === undefined) {
        current.delete(key);
      } else {
        current.set(key, value);
      }
    });

    // Create new URL
    const search = current.toString();
    const query = search ? `?${search}` : '';
    
    // Update URL without causing a page reload
    router.replace(`${pathname}${query}`, { scroll: false });
  }, [searchParams, router, pathname]);

  // Filter getters (read directly from URL)
  const getFilter = useCallback((key: string) => {
    return searchParams.get(key);
  }, [searchParams]);

  const getDateFilter = useCallback((key: string) => {
    const value = searchParams.get(key);
    return value ? new Date(value) : null;
  }, [searchParams]);

  const getBooleanFilter = useCallback((key: string) => {
    const value = searchParams.get(key);
    return value === 'true';
  }, [searchParams]);

  // Filter setters (write directly to URL)
  const setFilter = useCallback((key: string, value: string | null) => {
    updateUrl({ [key]: value });
  }, [updateUrl]);

  const setDateFilter = useCallback((key: string, value: Date | null) => {
    const formattedValue = value ? dayjs(value).format('YYYY-MM-DD') : null;
    updateUrl({ [key]: formattedValue });
  }, [updateUrl]);

  const setFilters = useCallback((filters: Record<string, string | Date | null>) => {
    const params: Record<string, string | null> = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value instanceof Date) {
        params[key] = dayjs(value).format('YYYY-MM-DD');
      } else {
        params[key] = value;
      }
    });
    
    updateUrl(params);
  }, [updateUrl]);

  const clearFilters = useCallback((keysToKeep: string[] = ['search', 'page']) => {
    const current = new URLSearchParams();
    
    // Keep only specified parameters
    keysToKeep.forEach(key => {
      const value = searchParams.get(key);
      if (value) {
        current.set(key, value);
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : '';
    router.replace(`${pathname}${query}`, { scroll: false });
  }, [searchParams, router, pathname]);

  // Handle search input changes
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    updateUrl({ 
      search: value || null,
      page: value !== searchTerm ? '1' : null // Reset to page 1 on new search
    });
  }, [updateUrl, searchTerm]);

  // Handle page changes
  const handlePageChange = useCallback((pageNumber: number) => {
    updateUrl({ page: pageNumber.toString() });
  }, [updateUrl]);

  // Build API query string from current URL parameters
  const buildApiQuery = useMemo(() => {
    const params = new URLSearchParams();
    
    // Add pagination
    params.set('page', currentPage.toString());
    params.set('limit', pageSize.toString());
    
    // Add all other URL parameters
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'limit') {
        params.set(key, value);
      }
    });

    return params.toString();
  }, [currentPage, pageSize, searchParams]);

  return {
    searchTerm,
    debouncedSearchTerm,
    currentPage,
    pageSize,
    getFilter,
    getDateFilter,
    getBooleanFilter,
    setFilter,
    setDateFilter,
    setFilters,
    clearFilters,
    handleSearchChange,
    handlePageChange,
    buildApiQuery: () => buildApiQuery,
  };
}
