import React, { useState } from 'react';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis } from 'lucide-react';
import { GetCategories } from '@/api/data';
import { StatusBadge } from '@/components/common/status-badge';
import Details from './details';
import Create from './create';

interface CategoryProps {
  openCreate: boolean;
  permitEdit: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

const Category: React.FC<CategoryProps> = ({
  openCreate,
  permitEdit,
  setOpenCreate,
}) => {
  const [open, setOpen] = useState(false);
  const { currentPage, pageSize, handlePageChange, buildApiQuery } = useUrlFilters({
    initialPageSize: 10,
  });

  const [detail, setDetail] = useState<any | null>(null);

  const { category, categoryLoading, mutate, categoryError } = GetCategories(
    `?${buildApiQuery()}`
  );
  const data = category?.data?.categories;
  const totalPages = category?.data?.totalPages ?? 0;

  const handleEventFromModal = (category: any) => {
    setDetail(category);
    setOpen(true);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date</th>
              <th className="table-style">Name</th>
              <th className="table-style">CreatedBy</th>
              <th className="table-style">Packages</th>
              <th className="table-style">Status</th>
              <th className="table-style">Edit</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {data?.map((category, index) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={category.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(category.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{category.name}</td>
                <td className="table-style">{category.createdBy}</td>
                <td className="table-style">{category.packagesCount}</td>
                <td className="table-style">
                  {category.isActive
                    ? StatusBadge({ status: 'active' })
                    : StatusBadge({ status: 'inactive' })}
                </td>
                <td className="table-style">
                  {permitEdit ? (
                    <Ellipsis
                      onClick={() => handleEventFromModal(category)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  ) : (
                    <Ellipsis className="w-4 h-4 text-gray-400" />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {totalPages === 0 && (
          <div className="border text-sm py-12 text-center dar:text-white">
            No data available
          </div>
        )}
        {categoryLoading && (
          <div className="bg-white py-24 text-center">
            <Loader2 className="animate-spin w-6 h-6 " />
            <span>Loading...</span>
          </div>
        )}
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </>
  );
};

export default Category;
