'use client';

import React, { useState, useEffect } from 'react';
import { useUrlSearchAndPagination } from '@/hooks/useUrlSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, CreditCard } from 'lucide-react';
import { GetTransactions, GetStaffTransactions } from '@/api/data';
import { numberFormat } from '@/lib/utils';
import { StatusBadge } from '@/components/common/status-badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';
import MealVoucherModal from '../../rewards/components/meal-voucher-modal';

const TransactionData = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    updateUrlParams,
    getUrlParam,
    buildApiQuery,
  } = useUrlSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [showMealVoucherModal, setShowMealVoucherModal] = useState(false);

  // Initialize filters from URL parameters
  const [viewType, setViewType] = useState<'my' | 'all'>(
    (getUrlParam('viewType') as 'my' | 'all') || 'my'
  );
  const [typeFilter, setTypeFilter] = useState(getUrlParam('type') || '');
  const [statusFilter, setStatusFilter] = useState(getUrlParam('status') || '');
  const [startDate, setStartDate] = useState<Date | undefined>(
    getUrlParam('startDate') ? new Date(getUrlParam('startDate')!) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    getUrlParam('endDate') ? new Date(getUrlParam('endDate')!) : undefined
  );

  const canEditTransactions = hasPermission(PERMISSIONS.TRANSACTION_EDIT);
  const canCreateTransactions = hasPermission(PERMISSIONS.TRANSACTION_CREATE);

  // Update URL parameters when filters change
  useEffect(() => {
    const params: Record<string, string | null> = {
      viewType: viewType,
      type: typeFilter || null,
      status: statusFilter && statusFilter !== 'all' ? statusFilter : null,
      startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : null,
      endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : null,
    };

    updateUrlParams(params);
  }, [viewType, typeFilter, statusFilter, startDate, endDate, updateUrlParams]);

  // Build API query string from URL parameters
  const apiQuery = buildApiQuery();

  // Conditionally use different APIs based on viewType and permissions
  const {
    transactions: myTransactions,
    transactionLoading: myTransactionLoading,
  } = GetStaffTransactions(`?${apiQuery}`);

  const {
    transactions: allTransactions,
    transactionLoading: allTransactionLoading,
    mutate: allTransactionMutate,
  } = canEditTransactions
    ? GetTransactions(`?${apiQuery}`)
    : { transactions: null, transactionLoading: false, mutate: () => {} };

  // Use appropriate data based on viewType
  const transactions = viewType === 'all' ? allTransactions : myTransactions;
  const transactionLoading =
    viewType === 'all' ? allTransactionLoading : myTransactionLoading;
  const data = transactions?.data?.transactions;
  const totalPages = transactions?.data?.totalPages ?? 0;

  // Check for pending transactions in all transactions
  const hasPendingTransactions =
    canEditTransactions &&
    allTransactions?.data?.transactions?.some(
      (transaction: any) => transaction.status.toLowerCase() === 'pending'
    );

  const handleEventFromModal = (transaction: any) => {
    setDetail(transaction);
    setOpen(true);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'SUCCESS', label: 'Success' },
    { value: 'FAILED', label: 'Failed' },
    { value: 'PROCESSING', label: 'Processing' },
    { value: 'CONFIRMED', label: 'Confirmed' },
    { value: 'CANCELLED', label: 'Cancelled' },
  ];

  const buttonOptions = [
    { label: 'All', param: '', className: '' },
    {
      label: 'Package',
      param: 'type=package',
      className:
        'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400',
    },
    {
      label: 'Transfer',
      param: 'type=transfer',
      className:
        'bg-teal-100 text-teal-600 hover:bg-teal-200 dark:bg-teal-900/30 dark:text-blue-teal',
    },
    {
      label: 'Withdrawal',
      param: 'type=withdrawal',
      className:
        'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400',
    },
    {
      label: 'Cafeteria',
      param: 'type=cafeteria',
      className:
        'bg-orange-100 text-orange-600 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-400',
    },
    {
      label: 'Reward',
      param: 'type=reward',
      className:
        'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400',
    },
    {
      label: 'Refund',
      param: 'type=refund',
      className:
        'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400',
    },
  ];

  const handleSelect = (param: string) => {
    setTypeFilter(param);
  };

  const handleViewTypeChange = (value: 'my' | 'all') => {
    setViewType(value);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="space-x-2 space-y-4">
            {buttonOptions.map(({ label, param, className }) => (
              <StatusBadge
                status={label.toLowerCase()}
                className={`cursor-pointer ${className} ${typeFilter === param ? 'ring-2 ring-offset-1' : ''}`}
                key={label}
                onClick={() => handleSelect(param)}
              />
            ))}
          </div>
          <div className="flex flex-wrap gap-3 items-center">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <DateRangeFilter
              startDate={startDate}
              endDate={endDate}
              onStartDateChange={(date) => setStartDate(date || undefined)}
              onEndDateChange={(date) => setEndDate(date || undefined)}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search transactions..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewType === 'my' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleViewTypeChange('my')}
              >
                My Transactions
              </Button>
              {canEditTransactions && (
                <Button
                  variant={viewType === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleViewTypeChange('all')}
                  className={`relative ${hasPendingTransactions && viewType !== 'all' ? 'after:absolute after:top-1 after:right-1 after:w-2 after:h-2 after:bg-yellow-500 after:rounded-full' : ''}`}
                >
                  All Transactions
                </Button>
              )}
              {canCreateTransactions && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setShowMealVoucherModal(true)}
                >
                  Meal Voucher
                </Button>
              )}
            </div>
          </div>
        </div>
        <table className="whitespace-nowrap w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Transaction Date</th>
              {/* <th className="table-style">Package Amount</th> */}
              <th className="table-style">Amount</th>
              <th className="table-style">Reference</th>
              <th className="table-style">Mode</th>
              <th className="table-style">Type</th>
              <th className="table-style">Status</th>
              <th className="table-style">More</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {transactionLoading ? (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading transactions...</span>
                  </div>
                </td>
              </tr>
            ) : data && data.length > 0 ? (
              data.map((transaction: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={transaction.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(transaction.createdAt).format('MMMM D, YYYY')}
                  </td>
                  <td className="table-style">
                    {numberFormat(transaction.amount)}
                  </td>
                  <td className="table-style">{transaction.reference}</td>
                  <td className="table-style capitalize">{transaction.mode}</td>
                  <td className="table-style">
                    <span className="font-semibold px-2 py-1 rounded-full text-[11px] bg-gray-100 text-gray-600">
                      {transaction.type}
                    </span>
                  </td>
                  <td className="table-style">
                    {StatusBadge({
                      status: transaction.status.toLowerCase(),
                    })}
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(transaction)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <CreditCard className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No transactions found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Transactions will appear here once they are created'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && (
        <Details
          open={open}
          setOpen={setOpen}
          mutate={allTransactionMutate}
          data={detail}
        />
      )}
      {showMealVoucherModal && (
        <MealVoucherModal
          open={showMealVoucherModal}
          setOpen={setShowMealVoucherModal}
          mutate={allTransactionMutate}
        />
      )}
    </>
  );
};

export default TransactionData;
