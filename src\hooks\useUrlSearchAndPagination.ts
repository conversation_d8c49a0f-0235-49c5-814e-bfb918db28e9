'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useDebounce } from './useDebounce';

interface UseUrlSearchAndPaginationProps {
  initialPageSize?: number;
  debounceDelay?: number;
}

interface UseUrlSearchAndPaginationReturn {
  // Search state
  searchTerm: string;
  debouncedSearchTerm: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

  // Pagination state
  currentPage: number;
  pageSize: number;
  handlePageChange: (pageNumber: number) => void;

  // URL parameter management
  updateUrlParams: (params: Record<string, string | null>) => void;
  getUrlParam: (key: string) => string | null;
  clearUrlParams: (keysToKeep?: string[]) => void;
  
  // Query building for API calls
  buildApiQuery: () => string;
}

export function useUrlSearchAndPagination({
  initialPageSize = 15,
  debounceDelay = 500,
}: UseUrlSearchAndPaginationProps = {}): UseUrlSearchAndPaginationReturn {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  
  const [pageSize] = useState(initialPageSize);
  
  // Initialize state from URL parameters
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get('page') || '1', 10)
  );
  
  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, debounceDelay);

  // Update URL parameters
  const updateUrlParams = useCallback((params: Record<string, string | null>) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    // Update or remove parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === '' || value === undefined) {
        current.delete(key);
      } else {
        current.set(key, value);
      }
    });

    // Create new URL
    const search = current.toString();
    const query = search ? `?${search}` : '';
    
    // Update URL without causing a page reload
    router.replace(`${pathname}${query}`, { scroll: false });
  }, [searchParams, router, pathname]);

  // Get URL parameter
  const getUrlParam = useCallback((key: string) => {
    return searchParams.get(key);
  }, [searchParams]);

  // Clear URL parameters (optionally keep some)
  const clearUrlParams = useCallback((keysToKeep: string[] = []) => {
    const current = new URLSearchParams();
    
    // Keep only specified parameters
    keysToKeep.forEach(key => {
      const value = searchParams.get(key);
      if (value) {
        current.set(key, value);
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : '';
    router.replace(`${pathname}${query}`, { scroll: false });
  }, [searchParams, router, pathname]);

  // Build API query string from current URL parameters
  const buildApiQuery = useCallback(() => {
    const params = new URLSearchParams();
    
    // Add pagination
    params.set('page', currentPage.toString());
    params.set('limit', pageSize.toString());
    
    // Add all other URL parameters
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'limit') {
        params.set(key, value);
      }
    });

    return params.toString();
  }, [currentPage, pageSize, searchParams]);

  // Handle search input changes
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
  }, []);

  // Handle page changes
  const handlePageChange = useCallback((pageNumber: number) => {
    setCurrentPage(pageNumber);
    updateUrlParams({ page: pageNumber.toString() });
  }, [updateUrlParams]);

  // Update URL when debounced search term changes
  useEffect(() => {
    const params: Record<string, string | null> = {
      search: debouncedSearchTerm || null,
      page: debouncedSearchTerm !== searchParams.get('search') ? '1' : null, // Reset to page 1 on new search
    };
    
    // Only update if search term actually changed
    if (debouncedSearchTerm !== searchParams.get('search')) {
      setCurrentPage(1);
      updateUrlParams(params);
    }
  }, [debouncedSearchTerm, searchParams, updateUrlParams]);

  // Sync current page with URL
  useEffect(() => {
    const urlPage = parseInt(searchParams.get('page') || '1', 10);
    if (urlPage !== currentPage) {
      setCurrentPage(urlPage);
    }
  }, [searchParams, currentPage]);

  return {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    updateUrlParams,
    getUrlParam,
    clearUrlParams,
    buildApiQuery,
  };
}
