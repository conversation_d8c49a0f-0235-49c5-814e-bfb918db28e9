'use client';

import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface DateRangeFilterProps {
  // Original interface
  onDateRangeChange?: (
    startDate: Date | undefined,
    endDate: Date | undefined
  ) => void;
  // New interface for URL-based filtering
  startDate?: Date | null;
  endDate?: Date | null;
  onStartDateChange?: (date: Date | null) => void;
  onEndDateChange?: (date: Date | null) => void;
  onClear?: () => void;
  // URL-based filter props
  onFilterChange?: (startDate: Date | null, endDate: Date | null) => void;
  initialStartDate?: Date | null;
  initialEndDate?: Date | null;
  className?: string;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  onDateRangeChange,
  startDate: externalStartDate,
  endDate: externalEndDate,
  onStartDateChange,
  onEndDateChange,
  onClear: externalClear,
  onFilterChange,
  initialStartDate,
  initialEndDate,
  className,
}) => {
  // Use internal state if external state is not provided
  const [internalStartDate, setInternalStartDate] = useState<Date | undefined>(
    initialStartDate || undefined
  );
  const [internalEndDate, setInternalEndDate] = useState<Date | undefined>(
    initialEndDate || undefined
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Determine if we're using internal, external, or URL-based state management
  const isExternalState =
    onStartDateChange !== undefined && onEndDateChange !== undefined;
  const isUrlBasedState = onFilterChange !== undefined;

  // For URL-based state, we need to track the current values
  const [urlStartDate, setUrlStartDate] = useState<Date | undefined>(
    initialStartDate ? new Date(initialStartDate) : undefined
  );
  const [urlEndDate, setUrlEndDate] = useState<Date | undefined>(
    initialEndDate ? new Date(initialEndDate) : undefined
  );

  // Update URL-based state when initial values change
  useEffect(() => {
    if (isUrlBasedState) {
      setUrlStartDate(initialStartDate ? new Date(initialStartDate) : undefined);
      setUrlEndDate(initialEndDate ? new Date(initialEndDate) : undefined);
    }
  }, [initialStartDate, initialEndDate, isUrlBasedState]);

  // Get the current start and end dates based on state management approach
  const startDate = isExternalState
    ? (externalStartDate as Date | undefined)
    : isUrlBasedState
      ? urlStartDate
      : internalStartDate;
  const endDate = isExternalState
    ? (externalEndDate as Date | undefined)
    : isUrlBasedState
      ? urlEndDate
      : internalEndDate;

  // Set start date function that works with all interfaces
  const setStartDate = (date: Date | undefined) => {
    if (isExternalState) {
      onStartDateChange?.(date as Date | null);
    } else if (isUrlBasedState) {
      setUrlStartDate(date);
    } else {
      setInternalStartDate(date);
    }
  };

  // Set end date function that works with all interfaces
  const setEndDate = (date: Date | undefined) => {
    if (isExternalState) {
      onEndDateChange?.(date as Date | null);
    } else if (isUrlBasedState) {
      setUrlEndDate(date);
    } else {
      setInternalEndDate(date);
    }
  };

  // Update parent component when date range changes (for original interface)
  useEffect(() => {
    if (onDateRangeChange) {
      onDateRangeChange(startDate, endDate);
    }
  }, [startDate, endDate, onDateRangeChange]);

  // Handle date selection
  const handleSelect = (date: Date | undefined) => {
    if (!startDate || (startDate && endDate)) {
      // If no start date is selected or both dates are already selected, set start date
      if (isUrlBasedState) {
        onFilterChange?.(date || null, null);
        setUrlStartDate(date);
        setUrlEndDate(undefined);
      } else if (isExternalState) {
        onStartDateChange?.(date as Date | null);
        onEndDateChange?.(null);
      } else {
        setStartDate(date);
        setEndDate(undefined);
      }
    } else {
      // If only start date is selected, set end date (allows same day selection)
      // Use date comparison that works correctly for same-day selection
      if (date && format(date, 'yyyy-MM-dd') >= format(startDate, 'yyyy-MM-dd')) {
        if (isUrlBasedState) {
          onFilterChange?.(startDate, date);
          setUrlStartDate(startDate);
          setUrlEndDate(date);
        } else if (isExternalState) {
          // Keep startDate and set endDate
          onEndDateChange?.(date as Date | null);
        } else {
          setEndDate(date);
        }
        setIsCalendarOpen(false);
      } else {
        // If new date is before start date, swap them
        if (isUrlBasedState) {
          onFilterChange?.(date || null, startDate);
          setUrlStartDate(date);
          setUrlEndDate(startDate);
        } else if (isExternalState) {
          onStartDateChange?.(date as Date | null);
          onEndDateChange?.(startDate as Date | null);
        } else {
          setEndDate(startDate);
          setStartDate(date);
        }
        setIsCalendarOpen(false);
      }
    }
  };

  // Clear date range
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (isExternalState) {
      onStartDateChange?.(null);
      onEndDateChange?.(null);
      externalClear?.();
    } else if (isUrlBasedState) {
      onFilterChange?.(null, null);
    } else {
      setInternalStartDate(undefined);
      setInternalEndDate(undefined);
      onDateRangeChange?.(undefined, undefined);
    }
  };

  // Format date range for display
  const formatDateRange = () => {
    if (startDate && endDate) {
      if (format(startDate, 'yyyy-MM-dd') === format(endDate, 'yyyy-MM-dd')) {
        return `${format(startDate, 'MMM d, yyyy')} (24 hours)`;
      }
      return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
    }
    // Handle case where only endDate is provided (same date scenario)
    if (endDate && !startDate) {
      return `${format(endDate, 'MMM d, yyyy')} (24 hours)`;
    }
    if (startDate) {
      return `${format(startDate, 'MMM d, yyyy')} - Select end date`;
    }
    return 'Filter by date';
  };

  return (
    <div className={cn('relative', className)}>
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              !startDate && !endDate && 'text-muted-foreground'
            )}
          >
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              <span>{formatDateRange()}</span>
            </div>
            {(startDate || endDate) && (
              <button
                type="button"
                onClick={handleClear}
                className="rounded p-1"
              >
                <X className="h-4 w-4 opacity-70" />
              </button>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={endDate || startDate}
            onSelect={handleSelect}
            defaultMonth={startDate}
            numberOfMonths={1}
            disabled={(date) => {
              // Disable dates more than 1 year in the past or future
              const oneYearAgo = new Date();
              oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
              const oneYearFromNow = new Date();
              oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
              return date < oneYearAgo || date > oneYearFromNow;
            }}
          />
          <div className="p-3 border-t border-border">
            <div className="flex justify-between items-center">
              <div className="text-sm">
                {startDate && (
                  <span className="font-medium">
                    {format(startDate, 'MMM d, yyyy')}
                  </span>
                )}
                {startDate && endDate && <span> - </span>}
                {endDate && (
                  <span className="font-medium">
                    {format(endDate, 'MMM d, yyyy')}
                  </span>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-7 px-3"
              >
                Clear
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DateRangeFilter;
